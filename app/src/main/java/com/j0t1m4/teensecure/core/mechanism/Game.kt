package com.j0t1m4.teensecure.core.mechanism


class Game {

    private var correctAnswersCount: Int = 0
    private var score: Int = 0
    private var totalPossibleScore: Int = 0
    private val passMark = 70
    var totalQuestions = 0  // You can adjust this based on your game's logic

    fun addScore(reward: Int, isCorrect: <PERSON><PERSON><PERSON>) {
        score += reward
        if (isCorrect) {
            correctAnswersCount++
        }
    }

    fun addPossibleScore(reward: Int) {
        totalPossibleScore += reward
    }

    fun hasPassed(): Boolean {
        return if (totalPossibleScore > 0) {
            (score * 100 / totalPossibleScore) >= passMark
        } else {
            false
        }
    }

    fun getPercentageScore(): Int {
        return if (totalPossibleScore > 0) {
            (score * 100 / totalPossibleScore)
        } else {
            0
        }
    }

    // Get total score at the end of the game
    fun getTotalScore(): Int {
        return score
    }

    // Get the number of correct answers at the end of the game
    fun getCorrectAnswersCount(): Int {
        return correctAnswersCount
    }

    // Get total possible score
    fun getTotalPossibleScore(): Int {
        return totalPossibleScore
    }

    // Reset game state
    fun reset() {
        correctAnswersCount = 0
        score = 0
        totalPossibleScore = 0
        totalQuestions = 0
    }
}
