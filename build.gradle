buildscript {
    ext.kotlin_version = '1.9.25'
    repositories {
        google()
        mavenCentral()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
        maven { url 'https://jitpack.io' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'androidx.navigation:navigation-safe-args-gradle-plugin:2.7.7'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        classpath 'com.google.dagger:hilt-android-gradle-plugin:2.49'
        classpath 'com.android.tools.build:bundletool:0.9.0'
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        maven { url 'https://jitpack.io' }
        mavenCentral()
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}