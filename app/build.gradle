plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    id "androidx.navigation.safeargs.kotlin"
    id 'dagger.hilt.android.plugin'
    id 'com.google.firebase.crashlytics'
}

android {
    namespace 'com.j0t1m4.teensecure'
    compileSdk 34

    defaultConfig {
        applicationId "com.j0t1m4.teensecure"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    android.applicationVariants.configureEach { variant ->
        variant.outputs.configureEach { output ->
            def formattedDate = new Date().format('dd-MMM-yyyy')
            outputFileName = "TeenSecure-${variant.buildType.name.toLowerCase()}-v${defaultConfig.versionName}-${formattedDate}.apk".replace("--", "-")
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        debug {
            minifyEnabled false
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        dataBinding true
        viewBinding true
    }
    configurations {
        all*.exclude module: 'okhttp'
        all*.exclude module: 'okio'
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.13.1'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.core:core-splashscreen:1.0.1'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    implementation 'androidx.vectordrawable:vectordrawable:1.2.0'

    // android support libraries
    implementation "androidx.browser:browser:1.8.0"
    implementation "androidx.recyclerview:recyclerview:1.3.2"
    implementation "androidx.activity:activity-ktx:1.9.1"
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.activity:activity:1.9.1'

    //Glide
    def glide_version = "4.12.0"
    implementation 'com.github.bumptech.glide:glide:4.13.2'
    kapt "com.github.bumptech.glide:compiler:$glide_version"

    //Dagger-Hilt
    //hilt for DI
    implementation "com.google.dagger:hilt-android:2.49"
    kapt "com.google.dagger:hilt-android-compiler:2.49"
    kapt 'androidx.hilt:hilt-compiler:1.2.0'

    //Kotlin Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    //EncryptedSharedPreferences
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'

    //navigation
    implementation 'androidx.navigation:navigation-fragment-ktx:2.8.0'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.7'

    // Application Lifecycle
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.4'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.8.4'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.8.4'

    // Timber
    implementation 'com.jakewharton.timber:timber:5.0.1'

    implementation platform('com.google.firebase:firebase-bom:33.2.0')
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics-ktx'

    implementation 'androidx.viewpager2:viewpager2:1.1.0'

    implementation 'com.afollestad.material-dialogs:core:3.3.0'

    // Lottiefiles animation
    implementation 'com.airbnb.android:lottie:6.5.2'

    implementation 'com.github.zhpanvip:viewpagerindicator:1.2.3'
    implementation "org.jetbrains.kotlin:kotlin-test:1.9.25"

}
kapt {
    correctErrorTypes true
}