<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Selected state -->
    <item android:state_selected="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/primaryColor" />
                    <corners android:radius="8dp" />
                </shape>
            </item>
            <item android:drawable="@drawable/ic_check_circle" android:gravity="top|end" android:top="8dp" android:end="8dp" />
        </layer-list>
    </item>
    
    <!-- Default state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>
