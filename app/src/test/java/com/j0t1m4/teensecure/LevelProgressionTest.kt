package com.j0t1m4.teensecure

import android.content.Context
import com.j0t1m4.teensecure.data.SharedPreferences
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class LevelProgressionTest {

    @Mock
    private lateinit var context: Context

    private lateinit var sharedPreferences: SharedPreferences

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        // Note: This test would need proper Android testing setup for SharedPreferences
        // For now, we'll test the logic conceptually
    }

    @Test
    fun `test level unlocking logic - beginner always unlocked`() {
        // Test that beginner level (1) is always unlocked for any topic
        val topics = listOf("phishing", "baiting", "impersonation", "cyberbullying")
        
        topics.forEach { topic ->
            // Beginner should always be unlocked regardless of progress
            assertTrue("Beginner level should always be unlocked for $topic", 
                isLevelUnlockedLogic(topic, 1, 1))
        }
    }

    @Test
    fun `test level unlocking logic - intermediate requires beginner completion`() {
        val topic = "phishing"
        
        // User hasn't completed beginner (still at level 1)
        assertFalse("Intermediate should be locked when beginner not completed",
            isLevelUnlockedLogic(topic, 2, 1))
        
        // User completed beginner (now at level 2)
        assertTrue("Intermediate should be unlocked when beginner completed",
            isLevelUnlockedLogic(topic, 2, 2))
        
        // User completed intermediate (now at level 3)
        assertTrue("Intermediate should remain unlocked when user progressed further",
            isLevelUnlockedLogic(topic, 2, 3))
    }

    @Test
    fun `test level unlocking logic - professional requires intermediate completion`() {
        val topic = "baiting"
        
        // User at beginner level
        assertFalse("Professional should be locked when only beginner completed",
            isLevelUnlockedLogic(topic, 3, 1))
        
        // User at intermediate level
        assertFalse("Professional should be locked when only intermediate completed",
            isLevelUnlockedLogic(topic, 3, 2))
        
        // User completed intermediate (now at level 3)
        assertTrue("Professional should be unlocked when intermediate completed",
            isLevelUnlockedLogic(topic, 3, 3))
    }

    @Test
    fun `test topic-specific progression`() {
        // Test that progress in one topic doesn't affect others
        val phishingLevel = 3  // Completed all levels
        val baitingLevel = 1   // Only beginner
        
        // Phishing - all levels should be unlocked
        assertTrue("Phishing beginner should be unlocked", 
            isLevelUnlockedLogic("phishing", 1, phishingLevel))
        assertTrue("Phishing intermediate should be unlocked", 
            isLevelUnlockedLogic("phishing", 2, phishingLevel))
        assertTrue("Phishing professional should be unlocked", 
            isLevelUnlockedLogic("phishing", 3, phishingLevel))
        
        // Baiting - only beginner should be unlocked
        assertTrue("Baiting beginner should be unlocked", 
            isLevelUnlockedLogic("baiting", 1, baitingLevel))
        assertFalse("Baiting intermediate should be locked", 
            isLevelUnlockedLogic("baiting", 2, baitingLevel))
        assertFalse("Baiting professional should be locked", 
            isLevelUnlockedLogic("baiting", 3, baitingLevel))
    }

    @Test
    fun `test level progression sequence`() {
        val topic = "impersonation"
        
        // Start with beginner unlocked only
        var currentLevel = 1
        assertTrue("Should start with beginner unlocked", 
            isLevelUnlockedLogic(topic, 1, currentLevel))
        assertFalse("Should start with intermediate locked", 
            isLevelUnlockedLogic(topic, 2, currentLevel))
        assertFalse("Should start with professional locked", 
            isLevelUnlockedLogic(topic, 3, currentLevel))
        
        // Complete beginner, unlock intermediate
        currentLevel = 2
        assertTrue("Beginner should remain unlocked", 
            isLevelUnlockedLogic(topic, 1, currentLevel))
        assertTrue("Intermediate should now be unlocked", 
            isLevelUnlockedLogic(topic, 2, currentLevel))
        assertFalse("Professional should still be locked", 
            isLevelUnlockedLogic(topic, 3, currentLevel))
        
        // Complete intermediate, unlock professional
        currentLevel = 3
        assertTrue("Beginner should remain unlocked", 
            isLevelUnlockedLogic(topic, 1, currentLevel))
        assertTrue("Intermediate should remain unlocked", 
            isLevelUnlockedLogic(topic, 2, currentLevel))
        assertTrue("Professional should now be unlocked", 
            isLevelUnlockedLogic(topic, 3, currentLevel))
    }

    // Helper method that mimics the logic from SharedPreferences.isLevelUnlocked
    private fun isLevelUnlockedLogic(topic: String, level: Int, currentTopicLevel: Int): Boolean {
        return when (level) {
            1 -> true // Beginner level is always unlocked
            2 -> currentTopicLevel >= 2 // Intermediate level is unlocked if Beginner is completed
            3 -> currentTopicLevel >= 3 // Advanced level is unlocked if Intermediate is completed
            else -> false
        }
    }
}
