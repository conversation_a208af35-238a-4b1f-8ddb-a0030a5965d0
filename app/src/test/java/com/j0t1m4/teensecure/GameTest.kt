package com.j0t1m4.teensecure

import com.j0t1m4.teensecure.core.mechanism.Game
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

class GameTest {

    private lateinit var game: Game

    @Before
    fun setUp() {
        game = Game()
    }

    @Test
    fun `test game initialization`() {
        assertEquals(0, game.getTotalScore())
        assertEquals(0, game.getCorrectAnswersCount())
        assertEquals(0, game.getTotalPossibleScore())
        assertEquals(0, game.getPercentageScore())
        assertFalse(game.hasPassed())
    }

    @Test
    fun `test adding scores and calculating percentage`() {
        // Add possible scores first
        game.addPossibleScore(10)
        game.addPossibleScore(15)
        game.addPossibleScore(20)
        // Total possible: 45

        // Add actual scores
        game.addScore(10, true)  // Correct answer
        game.addScore(0, false)  // Wrong answer
        game.addScore(20, true)  // Correct answer
        // Total score: 30

        assertEquals(30, game.getTotalScore())
        assertEquals(2, game.getCorrectAnswersCount())
        assertEquals(45, game.getTotalPossibleScore())
        assertEquals(66, game.getPercentageScore()) // 30/45 * 100 = 66.67 -> 66
    }

    @Test
    fun `test pass mark calculation`() {
        // Add possible scores
        game.addPossibleScore(10)
        game.addPossibleScore(10)
        game.addPossibleScore(10)
        game.addPossibleScore(10)
        // Total possible: 40

        // Score 28 out of 40 (70%)
        game.addScore(10, true)
        game.addScore(8, false)  // Partial credit
        game.addScore(10, true)
        game.addScore(0, false)

        assertEquals(28, game.getTotalScore())
        assertEquals(70, game.getPercentageScore())
        assertTrue(game.hasPassed()) // Should pass with exactly 70%
    }

    @Test
    fun `test failing score`() {
        // Add possible scores
        game.addPossibleScore(10)
        game.addPossibleScore(10)
        // Total possible: 20

        // Score 13 out of 20 (65%)
        game.addScore(10, true)
        game.addScore(3, false)

        assertEquals(13, game.getTotalScore())
        assertEquals(65, game.getPercentageScore())
        assertFalse(game.hasPassed()) // Should fail with 65%
    }

    @Test
    fun `test game reset`() {
        // Add some scores
        game.addPossibleScore(10)
        game.addScore(5, true)
        game.totalQuestions = 2

        // Verify state before reset
        assertTrue(game.getTotalScore() > 0)
        assertTrue(game.getCorrectAnswersCount() > 0)
        assertTrue(game.getTotalPossibleScore() > 0)

        // Reset and verify
        game.reset()
        assertEquals(0, game.getTotalScore())
        assertEquals(0, game.getCorrectAnswersCount())
        assertEquals(0, game.getTotalPossibleScore())
        assertEquals(0, game.totalQuestions)
        assertEquals(0, game.getPercentageScore())
    }

    @Test
    fun `test edge case - no possible score`() {
        // Don't add any possible scores
        game.addScore(10, true)

        assertEquals(10, game.getTotalScore())
        assertEquals(0, game.getPercentageScore()) // Should be 0 when no possible score
        assertFalse(game.hasPassed()) // Should fail when no possible score
    }
}
