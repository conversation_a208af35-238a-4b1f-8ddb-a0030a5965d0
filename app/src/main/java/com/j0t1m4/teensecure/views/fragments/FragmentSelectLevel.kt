package com.j0t1m4.teensecure.views.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.material.snackbar.Snackbar
import com.j0t1m4.teensecure.R
import com.j0t1m4.teensecure.data.SharedPreferences
import com.j0t1m4.teensecure.databinding.FragmentSelectLevelBinding
import com.j0t1m4.teensecure.views.activities.MainActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FragmentSelectLevel : Fragment() {
    private lateinit var binding: FragmentSelectLevelBinding
    private var selectedLevel: String? = null
    private val args: FragmentSelectLevelArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentSelectLevelBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (requireActivity() as MainActivity).setToolbarBgColor(R.color.secondaryBackground)
        updateLevelButtonStates()
        setupClickListeners()
    }

    private fun setupClickListeners() {
        val sharedPreferences = SharedPreferences(requireContext())
        val topic = args.topic

        binding.btnBeginnerDefault.setOnClickListener {
            handleLevelSelection("beginner")
        }

        binding.btnIntermediateDefault.setOnClickListener {
            if (sharedPreferences.isLevelUnlocked(topic, 2)) {
                handleLevelSelection("intermediate")
            } else {
                Snackbar.make(binding.root, "Oops you cannot attempt this level, kindly complete the Beginner level", Snackbar.LENGTH_LONG)
                    .setBackgroundTint(ContextCompat.getColor(requireContext(), R.color.red))
                    
                    .show()
            }
        }

        binding.btnProfessionalDefault.setOnClickListener {
            if (sharedPreferences.isLevelUnlocked(topic, 3)) {
                handleLevelSelection("professional")
            } else {
                Snackbar.make(binding.root, "Oops you cannot attempt this level, kindly complete the Intermediate level", Snackbar.LENGTH_LONG)
                    .setBackgroundTint(ContextCompat.getColor(requireContext(), R.color.red))
                    .show()            }
        }

        binding.btnProceed.setOnClickListener {
            if (selectedLevel == null) {
                Snackbar.make(binding.root, "Please select a level to continue", Snackbar.LENGTH_LONG)
                    .setBackgroundTint(ContextCompat.getColor(requireContext(), R.color.red))
                    .show()
            } else {
                navigateToNextFragment(selectedLevel!!)
            }
        }
    }

    private fun handleLevelSelection(level: String) {
        selectedLevel = level
        binding.tvSelectedLevel.text = "Selected Level: $level"
    }

    private fun navigateToNextFragment(level: String) {
        FragmentSelectLevelDirections.actionFragmentSelectLevelToFragmentSelectedLevel(level, args.topic).apply {
            findNavController().navigate(this)
        }
    }

    private fun updateLevelButtonStates() {
        val sharedPreferences = SharedPreferences(requireContext())
        val topic = args.topic

        // Update intermediate button state
        val isIntermediateUnlocked = sharedPreferences.isLevelUnlocked(topic, 2)
        binding.btnIntermediateDefault.apply {
            alpha = if (isIntermediateUnlocked) 1.0f else 0.5f
            isEnabled = isIntermediateUnlocked
        }

        // Update professional button state
        val isProfessionalUnlocked = sharedPreferences.isLevelUnlocked(topic, 3)
        binding.btnProfessionalDefault.apply {
            alpha = if (isProfessionalUnlocked) 1.0f else 0.5f
            isEnabled = isProfessionalUnlocked
        }

        // Beginner is always unlocked
        binding.btnBeginnerDefault.apply {
            alpha = 1.0f
            isEnabled = true
        }
    }

}