package com.j0t1m4.teensecure.views.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.recyclerview.widget.RecyclerView
import com.j0t1m4.teensecure.databinding.ItemMatchingPairBinding

class MatchingAdapter(private val pairs: Map<String, String>) : RecyclerView.Adapter<MatchingAdapter.MatchingPairViewHolder>() {

    private val userMatches: MutableMap<String, String> = mutableMapOf()
    private val pairsList = pairs.toList()
    private val allOptions = pairs.values.shuffled() // Shuffle options for better UX

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MatchingPairViewHolder {
        val binding = ItemMatchingPairBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MatchingPairViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MatchingPairViewHolder, position: Int) {
        holder.bind(pairsList[position])
    }

    override fun getItemCount(): Int = pairsList.size

    // Function to retrieve user-selected matches
    fun getUserMatches(): Map<String, String> = userMatches

    inner class MatchingPairViewHolder(private val binding: ItemMatchingPairBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(pair: Pair<String, String>) {
            binding.termTextView.text = pair.first

            // Add a default "Select an option" item
            val dropdownOptions = listOf("Select an option") + allOptions

            // Populate the dropdown (Spinner) with the possible answers (right-side values)
            val adapter = ArrayAdapter(
                binding.root.context,
                android.R.layout.simple_spinner_item,
                dropdownOptions
            )
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.matchDropdown.adapter = adapter

            // Set up the listener for the dropdown (Spinner)
            binding.matchDropdown.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>, view: View?, position: Int, id: Long) {
                    if (position > 0) { // Skip the "Select an option" item
                        // Store the user's selection in the userMatches map
                        userMatches[pair.first] = dropdownOptions[position]
                    } else {
                        // Remove the selection if "Select an option" is chosen
                        userMatches.remove(pair.first)
                    }
                }

                override fun onNothingSelected(parent: AdapterView<*>) {
                    // Remove the selection if nothing is selected
                    userMatches.remove(pair.first)
                }
            }
        }
    }

}
